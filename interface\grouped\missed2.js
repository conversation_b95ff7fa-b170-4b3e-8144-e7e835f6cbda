/**
 * Node.js script to compare JSON files containing product data.
 * It can identify products present in a 'source' file but missing from one or more 'target' files.
 * The script logs the total count of products in each input file and the comparison results.
 */

const fs = require('fs'); // Import the Node.js file system module for reading and writing files.

/**
 * Compares a source JSON file against one or two target JSON files to find products
 * present in the source but missing from the specified targets.
 *
 * @param {string} sourceFilePath - The path to the source JSON file.
 * @param {string} target1FilePath - The path to the first target JSON file.
 * @param {string | null} target2FilePath - The path to the second target JSON file (or null if only one target).
 * @param {string} outputFilePath - The path where the missing products will be saved.
 * @param {string} comparisonType - A string describing the comparison (e.g., "missing from", "missing from BOTH").
 */
function compareJsonFiles(sourceFilePath, target1FilePath, target2FilePath, outputFilePath, comparisonType) {
    try {
        console.log(`\n--- Starting Comparison: ${comparisonType} ${target1FilePath}${target2FilePath ? ` and ${target2FilePath}` : ''} ---`);

        // --- Step 1: Read and Parse JSON Files ---

        // Read and parse the source JSON file.
        const sourceDataRaw = fs.readFileSync(sourceFilePath, 'utf8');
        const sourceData = JSON.parse(sourceDataRaw);
        // Extract all unique product numbers from the source data into a Map for efficient lookup.
        const productsInSource = new Map();
        sourceData.forEach(group => {
            for (const productNumber in group) {
                if (Object.hasOwnProperty.call(group, productNumber)) {
                    productsInSource.set(productNumber, group[productNumber]);
                }
            }
        });
        console.log(`Successfully loaded and parsed source file: ${sourceFilePath} (Total unique products: ${productsInSource.size})`);


        // Read and parse the first target JSON file.
        const target1DataRaw = fs.readFileSync(target1FilePath, 'utf8');
        const target1Data = JSON.parse(target1DataRaw);
        // Extract all unique product numbers from the first target data into a Map.
        const productsInTarget1 = new Map();
        target1Data.forEach(group => {
            for (const productNumber in group) {
                if (Object.hasOwnProperty.call(group, productNumber)) {
                    productsInTarget1.set(productNumber, group[productNumber]);
                }
            }
        });
        console.log(`Successfully loaded and parsed target file 1: ${target1FilePath} (Total unique products: ${productsInTarget1.size})`);


        let productsInTarget2 = new Map(); // Initialize as empty Map
        if (target2FilePath) {
            // Read and parse the second target JSON file, if provided.
            const target2DataRaw = fs.readFileSync(target2FilePath, 'utf8');
            const target2Data = JSON.parse(target2DataRaw);
            // Extract all unique product numbers from the second target data into a Map.
            target2Data.forEach(group => {
                for (const productNumber in group) {
                    if (Object.hasOwnProperty.call(group, productNumber)) {
                        productsInTarget2.set(productNumber, group[productNumber]);
                    }
                }
            });
            console.log(`Successfully loaded and parsed target file 2: ${target2FilePath} (Total unique products: ${productsInTarget2.size})`);
        }

        // --- Step 3: Compare and Identify Missing Products ---

        const productsNotInTargets = {}; // Object to store products found in source but not in specified targets.
        let missedCount = 0;             // Counter for the total number of such products.

        // Iterate over each product in the source Map.
        productsInSource.forEach((productName, productNumber) => {
            let isMissing = false;
            if (target2FilePath) {
                // Logic for missing from BOTH target1 AND target2
                isMissing = !productsInTarget1.has(productNumber) && !productsInTarget2.has(productNumber);
            } else {
                // Logic for missing from ONLY target1
                isMissing = !productsInTarget1.has(productNumber);
            }

            if (isMissing) {
                productsNotInTargets[productNumber] = productName;
                missedCount++;
            }
        });

        // --- Step 4: Log Results and Save to Output File ---

        console.log(`\n--- Comparison Results ---`);
        console.log(`Total number of products found in "${sourceFilePath}" that are ${comparisonType} ${target1FilePath}${target2FilePath ? ` and ${target2FilePath}` : ''}: ${missedCount}`);

        fs.writeFileSync(outputFilePath, JSON.stringify(productsNotInTargets, null, 2), 'utf8');
        console.log(`Missing products saved to "${outputFilePath}" successfully.`);

    } catch (error) {
        // --- Error Handling ---
        console.error("\nAn error occurred during file comparison:");
        console.error(error.message);
        if (error.code === 'ENOENT') {
            console.error("Please ensure all required input JSON files exist in the same directory as this script.");
        }
    }
}

// --- Define File Paths and Run Comparison Scenarios ---

// Scenario 1: Products in final_matches.json missing from grouped_matches.json
console.log("\n--- Running Scenario 1: Products in final_matches.json missing from grouped_matches.json ---");
compareJsonFiles(
    'final_matches.json',      // sourceFilePath
    'grouped_matches.json',    // target1FilePath
    null,                      // target2FilePath (set to null for single target comparison)
    'final_missing_from_grouped.json', // outputFilePath
    'missing from'             // comparisonType for logging
);

// Scenario 2: Products in final_matches.json missing from BOTH grouped_matches.json and orphan_matches.json
console.log("\n--- Running Scenario 2: Products in final_matches.json missing from BOTH grouped_matches.json and orphan_matches.json ---");
compareJsonFiles(
    'final_matches.json',      // sourceFilePath
    'grouped_matches.json',    // target1FilePath
    'orphan_matches.json',     // target2FilePath
    'final_missing_from_grouped_and_orphan.json', // outputFilePath
    'missing from BOTH'        // comparisonType for logging
);
