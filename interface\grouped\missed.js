/**
 * Node.js script to compare two JSON files containing product data.
 * It identifies products present in the 'source' file but missing from the 'target' file.
 * The script logs the total count of missing products and saves them to a new JSON file.
 */

const fs = require('fs'); // Import the Node.js file system module for reading and writing files.

/**
 * Compares two JSON files to find products present in the source file
 * but missing from the target file.
 *
 * @param {string} sourceFilePath - The path to the source JSON file (e.g., 'grouped_matches_nont.json').
 * @param {string} targetFilePath - The path to the target JSON file (e.g., 'grouped_matches.json').
 * @param {string} outputFilePath - The path where the missing products will be saved (e.g., 'missed_grouped.json').
 */
function compareJsonFiles(sourceFilePath, targetFilePath, outputFilePath) {
    try {
        // --- Step 1: Read and Parse JSON Files ---

        // Read the content of the source JSON file synchronously.
        const sourceDataRaw = fs.readFileSync(sourceFilePath, 'utf8');
        // Parse the raw JSON string into a JavaScript array of objects.
        const sourceData = JSON.parse(sourceDataRaw);
        console.log(`Successfully loaded and parsed source file: ${sourceFilePath}`);

        // Read the content of the target JSON file synchronously.
        const targetDataRaw = fs.readFileSync(targetFilePath, 'utf8');
        // Parse the raw JSON string into a JavaScript array of objects.
        const targetData = JSON.parse(targetDataRaw);
        console.log(`Successfully loaded and parsed target file: ${targetFilePath}`);

        // --- Step 2: Extract Product Data into Maps for Efficient Lookup ---

        // Use Maps to store productNumber: productName pairs for quick lookups.
        // A Map is generally more performant for large datasets than a plain object
        // when keys might not always be simple strings (though here they are).
        const productsInTarget = new Map(); // Stores all products from grouped_matches.json
        const productsInSource = new Map();  // Stores all products from grouped_matches_nont.json

        // Populate productsInTarget Map:
        // Iterate over each 'group' object in the targetData array.
        targetData.forEach(group => {
            // Iterate over each key-value pair (productNumber: productName) within the group object.
            for (const productNumber in group) {
                // Ensure the property belongs to the object itself and not its prototype chain.
                if (Object.hasOwnProperty.call(group, productNumber)) {
                    // Add the product number and name to the map.
                    productsInTarget.set(productNumber, group[productNumber]);
                }
            }
        });
        console.log(`Extracted ${productsInTarget.size} unique products from ${targetFilePath}.`);

        // Populate productsInSource Map:
        // Iterate over each 'group' object in the sourceData array.
        sourceData.forEach(group => {
            // Iterate over each key-value pair (productNumber: productName) within the group object.
            for (const productNumber in group) {
                // Ensure the property belongs to the object itself.
                if (Object.hasOwnProperty.call(group, productNumber)) {
                    // Add the product number and name to the map.
                    productsInSource.set(productNumber, group[productNumber]);
                }
            }
        });
        console.log(`Extracted ${productsInSource.size} unique products from ${sourceFilePath}.`);

        // --- Step 3: Compare and Identify Missing Products ---

        const missedProducts = {}; // Object to store the products found in source but missing from target.
        let missedCount = 0;       // Counter for the total number of missing products.

        // Iterate over each product in the productsInSource Map.
        productsInSource.forEach((productName, productNumber) => {
            // Check if the current productNumber from the source file DOES NOT exist in the productsInTarget Map.
            if (!productsInTarget.has(productNumber)) {
                // If it's missing, add it to our missedProducts object.
                missedProducts[productNumber] = productName;
                // Increment the counter.
                missedCount++;
            }
        });

        // --- Step 4: Log Results and Save to Output File ---

        // Log the total number of products found in the source file that are missing from the target file.
        console.log(`\n--- Comparison Results ---`);
        console.log(`Total number of products found in "${sourceFilePath}" that are missing from "${targetFilePath}": ${missedCount}`);

        // Save the identified missing products to the specified output JSON file.
        // JSON.stringify(missedProducts, null, 2) formats the JSON with 2-space indentation for readability.
        fs.writeFileSync(outputFilePath, JSON.stringify(missedProducts, null, 2), 'utf8');
        console.log(`Missing products saved to "${outputFilePath}" successfully.`);

    } catch (error) {
        // --- Error Handling ---
        console.error("\nAn error occurred during file comparison:");
        console.error(error.message);
        // Provide a more specific message if the file is not found.
        if (error.code === 'ENOENT') {
            console.error("Please ensure both input JSON files ('grouped_matches_nont.json' and 'grouped_matches.json') exist in the same directory as this script.");
        }
    }
}

// --- Define File Paths ---
// IMPORTANT: Make sure these file names match your actual JSON file names.
const sourceFile = 'final_matches.json'; // The file you want to check for missing items in the other.
const targetFile = 'grouped_matches_nont.json';       // The file against which you are checking for missing items.
const outputFile = 'missed_grouped_nont.json';       // The file where the results will be saved.

// --- Execute the Comparison Function ---
compareJsonFiles(sourceFile, targetFile, outputFile);
