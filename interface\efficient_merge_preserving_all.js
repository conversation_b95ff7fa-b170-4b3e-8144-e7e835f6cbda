const fs = require('fs');

// Function to get the seller from a product key
function getSeller(productKey) {
    return productKey.split('_')[0];
}

// Function to count unique products in an array of groups
function countUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts.size;
}

async function efficientMergePreservingAll() {
    try {
        console.log('=== EFFICIENT MERGE PRESERVING ALL PRODUCTS ===\n');
        
        // Step 1: Read the original final_matches.json
        const data = await fs.promises.readFile('final_matches.json', 'utf8');
        const finalMatches = JSON.parse(data);
        
        console.log(`📊 Input: final_matches.json contains ${finalMatches.length} groups`);
        
        // Count unique products in original data
        const originalUniqueProducts = countUniqueProducts(finalMatches);
        console.log(`📊 Original unique products: ${originalUniqueProducts}`);
        
        // Step 2: Build product-to-groups mapping
        console.log('🔄 Building product-to-groups mapping...');
        const productToGroups = {};
        finalMatches.forEach((group, groupIndex) => {
            Object.keys(group).forEach(productKey => {
                if (!productToGroups[productKey]) {
                    productToGroups[productKey] = [];
                }
                productToGroups[productKey].push(groupIndex);
            });
        });
        
        // Step 3: Use Union-Find to create connected components
        console.log('🔄 Creating connected components...');
        const parent = Array.from({length: finalMatches.length}, (_, i) => i);
        const rank = new Array(finalMatches.length).fill(0);
        
        function find(x) {
            if (parent[x] !== x) {
                parent[x] = find(parent[x]);
            }
            return parent[x];
        }
        
        function union(x, y) {
            const rootX = find(x);
            const rootY = find(y);
            if (rootX !== rootY) {
                if (rank[rootX] < rank[rootY]) {
                    parent[rootX] = rootY;
                } else if (rank[rootX] > rank[rootY]) {
                    parent[rootY] = rootX;
                } else {
                    parent[rootY] = rootX;
                    rank[rootX]++;
                }
            }
        }
        
        // Union groups that share products
        let connectionsProcessed = 0;
        Object.values(productToGroups).forEach(groupIndices => {
            if (groupIndices.length > 1) {
                for (let i = 1; i < groupIndices.length; i++) {
                    union(groupIndices[0], groupIndices[i]);
                    connectionsProcessed++;
                }
            }
        });
        
        console.log(`📊 Processed ${connectionsProcessed} connections`);
        
        // Step 4: Group the original groups by their connected components
        console.log('🔄 Grouping by connected components...');
        const componentGroups = {};
        finalMatches.forEach((group, groupIndex) => {
            const root = find(groupIndex);
            if (!componentGroups[root]) {
                componentGroups[root] = [];
            }
            componentGroups[root].push(group);
        });
        
        // Step 5: Merge groups within each connected component
        console.log('🔄 Merging groups within components...');
        const mergedGroups = [];
        let componentCount = 0;
        
        Object.values(componentGroups).forEach(groupsToMerge => {
            const mergedGroup = {};
            groupsToMerge.forEach(group => {
                Object.assign(mergedGroup, group);
            });
            mergedGroups.push(mergedGroup);
            componentCount++;
            
            if (componentCount % 1000 === 0) {
                console.log(`   Processed ${componentCount} components...`);
            }
        });
        
        console.log(`📊 After merging: ${mergedGroups.length} groups`);
        
        // Step 6: Verify we preserved all products
        const mergedUniqueProducts = countUniqueProducts(mergedGroups);
        console.log(`📊 Merged unique products: ${mergedUniqueProducts}`);
        
        if (mergedUniqueProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved during merge!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - mergedUniqueProducts} products during merge!`);
            return;
        }
        
        // Step 7: Save merged groups
        console.log('💾 Saving merged_groups.json...');
        await fs.promises.writeFile('merged_groups.json', JSON.stringify(mergedGroups, null, 2));
        console.log(`📁 Saved: merged_groups.json with ${mergedGroups.length} groups`);
        
        // Step 8: Calculate group statistics
        const groupSizes = mergedGroups.map(group => Object.keys(group).length);
        const maxGroupSize = Math.max(...groupSizes);
        const avgGroupSize = groupSizes.reduce((a, b) => a + b, 0) / groupSizes.length;
        
        console.log(`📊 Largest merged group: ${maxGroupSize} products`);
        console.log(`📊 Average merged group size: ${avgGroupSize.toFixed(2)} products`);
        
        // Show size distribution
        const sizeDistribution = {};
        groupSizes.forEach(size => {
            if (size === 1) {
                sizeDistribution['1'] = (sizeDistribution['1'] || 0) + 1;
            } else if (size <= 10) {
                sizeDistribution['2-10'] = (sizeDistribution['2-10'] || 0) + 1;
            } else if (size <= 50) {
                sizeDistribution['11-50'] = (sizeDistribution['11-50'] || 0) + 1;
            } else if (size <= 100) {
                sizeDistribution['51-100'] = (sizeDistribution['51-100'] || 0) + 1;
            } else if (size <= 500) {
                sizeDistribution['101-500'] = (sizeDistribution['101-500'] || 0) + 1;
            } else if (size <= 1000) {
                sizeDistribution['501-1000'] = (sizeDistribution['501-1000'] || 0) + 1;
            } else {
                sizeDistribution['1000+'] = (sizeDistribution['1000+'] || 0) + 1;
            }
        });
        
        console.log('\n📊 Group size distribution:');
        Object.entries(sizeDistribution).forEach(([range, count]) => {
            console.log(`   ${range} products: ${count} groups`);
        });
        
        // Step 9: Phase 2 - Separate ready matches from orphans
        console.log('\n=== PHASE 2: SEPARATING READY MATCHES FROM ORPHANS ===');
        
        const readyMatches = [];
        const orphanMatches = [];
        
        mergedGroups.forEach(group => {
            const productKeys = Object.keys(group);
            const sellers = new Set();
            
            productKeys.forEach(key => {
                sellers.add(getSeller(key));
            });
            
            // Ready matches: more than 1 product AND more than 1 seller
            if (productKeys.length > 1 && sellers.size > 1) {
                readyMatches.push(group);
            } else {
                orphanMatches.push(group);
            }
        });
        
        // Step 10: Save phase 2 results
        console.log('💾 Saving phase 2 results...');
        await fs.promises.writeFile('ready_matches.json', JSON.stringify(readyMatches, null, 2));
        await fs.promises.writeFile('orphan.json', JSON.stringify(orphanMatches, null, 2));
        
        console.log(`📁 Saved: ready_matches.json with ${readyMatches.length} groups`);
        console.log(`📁 Saved: orphan.json with ${orphanMatches.length} groups`);
        
        // Step 11: Final verification
        const readyUniqueProducts = countUniqueProducts(readyMatches);
        const orphanUniqueProducts = countUniqueProducts(orphanMatches);
        const totalFinalProducts = readyUniqueProducts + orphanUniqueProducts;
        
        console.log(`\n=== FINAL VERIFICATION ===`);
        console.log(`📊 Ready matches unique products: ${readyUniqueProducts}`);
        console.log(`📊 Orphan unique products: ${orphanUniqueProducts}`);
        console.log(`📊 Total final unique products: ${totalFinalProducts}`);
        
        if (totalFinalProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved throughout the entire process!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - totalFinalProducts} products in phase 2!`);
        }
        
        // Step 12: Show statistics for ready matches
        const readyGroupSizes = readyMatches.map(group => Object.keys(group).length);
        if (readyGroupSizes.length > 0) {
            const readyMaxSize = Math.max(...readyGroupSizes);
            const readyAvgSize = readyGroupSizes.reduce((a, b) => a + b, 0) / readyGroupSizes.length;
            
            console.log(`\n📊 Ready matches - Largest group: ${readyMaxSize} products`);
            console.log(`📊 Ready matches - Average group size: ${readyAvgSize.toFixed(2)} products`);
            
            // Check for sham_2063721
            let shamGroup = null;
            let shamGroupSize = 0;
            for (let i = 0; i < readyMatches.length; i++) {
                if (readyMatches[i]['sham_2063721']) {
                    shamGroup = readyMatches[i];
                    shamGroupSize = Object.keys(shamGroup).length;
                    console.log(`\n🔍 sham_2063721 found in ready matches group with ${shamGroupSize} products`);
                    break;
                }
            }
            
            if (!shamGroup) {
                // Check in orphans
                for (let i = 0; i < orphanMatches.length; i++) {
                    if (orphanMatches[i]['sham_2063721']) {
                        shamGroup = orphanMatches[i];
                        shamGroupSize = Object.keys(shamGroup).length;
                        console.log(`\n🔍 sham_2063721 found in orphan group with ${shamGroupSize} products`);
                        break;
                    }
                }
            }
            
            // Show some products from sham group if it's large
            if (shamGroup && shamGroupSize > 20) {
                console.log('\n📋 Sample products from sham_2063721 group:');
                const entries = Object.entries(shamGroup);
                entries.slice(0, 10).forEach(([key, value]) => {
                    console.log(`   ${key}: ${value}`);
                });
                if (entries.length > 10) {
                    console.log(`   ... and ${entries.length - 10} more products`);
                }
            }
        }
        
        console.log('\n✅ PROCESS COMPLETED SUCCESSFULLY!');
        console.log(`\n📋 SUMMARY:`);
        console.log(`   • Original groups: ${finalMatches.length}`);
        console.log(`   • Merged groups: ${mergedGroups.length}`);
        console.log(`   • Ready matches: ${readyMatches.length}`);
        console.log(`   • Orphan groups: ${orphanMatches.length}`);
        console.log(`   • Products preserved: ${totalFinalProducts}/${originalUniqueProducts} ✅`);
        
    } catch (error) {
        console.error('❌ Error in efficient merge:', error);
    }
}

efficientMergePreservingAll();
