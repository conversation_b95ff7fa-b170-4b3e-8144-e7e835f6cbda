const fs = require('fs');

const finalMatchesPath = 'final_matches.json';
const groupedMatchesPath = 'merged_groups.json'; // Changed to match new naming
const readyMatchesPath = 'ready_matches.json';
const orphanMatchesPath = 'orphan.json'; // Changed to match user's request

// Function to get the seller from a product key
function getSeller(productKey) {
    return productKey.split('_')[0];
}

// Function to count unique products in an array of groups
function countUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts.size;
}

async function processMatches() {
    try {
        const data = await fs.promises.readFile(finalMatchesPath, 'utf8');
        const finalMatches = JSON.parse(data);

        console.log(`Objects before processing (final_matches.json): ${finalMatches.length}`);

        // Count unique products in original data
        const originalUniqueProducts = countUniqueProducts(finalMatches);
        console.log(`Original unique products: ${originalUniqueProducts}`);

        // Step 1: Build product-to-groups mapping
        console.log('Building product-to-groups mapping...');
        const productToGroups = {};
        finalMatches.forEach((group, groupIndex) => {
            Object.keys(group).forEach(productKey => {
                if (!productToGroups[productKey]) {
                    productToGroups[productKey] = [];
                }
                productToGroups[productKey].push(groupIndex);
            });
        });

        // Step 2: Use Union-Find to create connected components
        console.log('Creating connected components...');
        const parent = Array.from({length: finalMatches.length}, (_, i) => i);
        const rank = new Array(finalMatches.length).fill(0);

        function find(x) {
            if (parent[x] !== x) {
                parent[x] = find(parent[x]);
            }
            return parent[x];
        }

        function union(x, y) {
            const rootX = find(x);
            const rootY = find(y);
            if (rootX !== rootY) {
                if (rank[rootX] < rank[rootY]) {
                    parent[rootX] = rootY;
                } else if (rank[rootX] > rank[rootY]) {
                    parent[rootY] = rootX;
                } else {
                    parent[rootY] = rootX;
                    rank[rootX]++;
                }
            }
        }

        // Union groups that share products
        let connectionsProcessed = 0;
        Object.values(productToGroups).forEach(groupIndices => {
            if (groupIndices.length > 1) {
                for (let i = 1; i < groupIndices.length; i++) {
                    union(groupIndices[0], groupIndices[i]);
                    connectionsProcessed++;
                }
            }
        });

        console.log(`Processed ${connectionsProcessed} connections`);

        // Step 3: Group the original groups by their connected components
        console.log('Grouping by connected components...');
        const componentGroups = {};
        finalMatches.forEach((group, groupIndex) => {
            const root = find(groupIndex);
            if (!componentGroups[root]) {
                componentGroups[root] = [];
            }
            componentGroups[root].push(group);
        });

        // Step 4: Merge groups within each connected component
        console.log('Merging groups within components...');
        const consolidatedGroups = [];

        Object.values(componentGroups).forEach(groupsToMerge => {
            const mergedGroup = {};
            groupsToMerge.forEach(group => {
                Object.assign(mergedGroup, group);
            });
            consolidatedGroups.push(mergedGroup);
        });

        console.log(`After merging: ${consolidatedGroups.length} groups`);

        // Step 5: Verify we preserved all products
        const mergedUniqueProducts = countUniqueProducts(consolidatedGroups);
        console.log(`Merged unique products: ${mergedUniqueProducts}`);

        if (mergedUniqueProducts === originalUniqueProducts) {
            console.log('SUCCESS: All products preserved during merge!');
        } else {
            console.log(`ERROR: Lost ${originalUniqueProducts - mergedUniqueProducts} products during merge!`);
            return;
        }

        // Save the consolidated match groups
        await fs.promises.writeFile(groupedMatchesPath, JSON.stringify(consolidatedGroups, null, 2));
        console.log(`Objects in ${groupedMatchesPath}: ${consolidatedGroups.length}`);


        // Step 6: Calculate group statistics
        const groupSizes = consolidatedGroups.map(group => Object.keys(group).length);
        const maxGroupSize = Math.max(...groupSizes);
        const avgGroupSize = groupSizes.reduce((a, b) => a + b, 0) / groupSizes.length;

        console.log(`Largest merged group: ${maxGroupSize} products`);
        console.log(`Average merged group size: ${avgGroupSize.toFixed(2)} products`);

        // Step 7: Phase 2 - Separate ready matches from orphans
        console.log('\n=== PHASE 2: SEPARATING READY MATCHES FROM ORPHANS ===');

        const readyMatches = [];
        const orphanMatches = [];
        let objectsWithSingleProduct = 0;
        let objectsWithSameSellerProducts = 0;

        consolidatedGroups.forEach(matchObject => {
            const productKeys = Object.keys(matchObject);
            const sellers = new Set();

            productKeys.forEach(key => {
                sellers.add(getSeller(key));
            });

            // Log statistics
            if (productKeys.length === 1) {
                objectsWithSingleProduct++;
            }

            if (sellers.size === 1) {
                objectsWithSameSellerProducts++;
            }

            // An object qualifies for ready_matches if it contains:
            // 1. More than one product (key-value pair) AND
            // 2. Products from more than one distinct seller
            if (productKeys.length > 1 && sellers.size > 1) {
                readyMatches.push(matchObject);
            } else {
                orphanMatches.push(matchObject);
            }
        });

        await fs.promises.writeFile(readyMatchesPath, JSON.stringify(readyMatches, null, 2));
        await fs.promises.writeFile(orphanMatchesPath, JSON.stringify(orphanMatches, null, 2));

        // Step 8: Final verification
        const readyUniqueProducts = countUniqueProducts(readyMatches);
        const orphanUniqueProducts = countUniqueProducts(orphanMatches);
        const totalFinalProducts = readyUniqueProducts + orphanUniqueProducts;

        console.log(`Objects after splitting (${readyMatchesPath} + ${orphanMatchesPath}): ${readyMatches.length + orphanMatches.length}`);
        console.log(`Objects with only one product record: ${objectsWithSingleProduct}`);
        console.log(`Objects with all products from the same seller: ${objectsWithSameSellerProducts}`);

        // Step 9: Show statistics for ready matches
        const readyGroupSizes = readyMatches.map(group => Object.keys(group).length);
        if (readyGroupSizes.length > 0) {
            const readyMaxSize = Math.max(...readyGroupSizes);
            const readyAvgSize = readyGroupSizes.reduce((a, b) => a + b, 0) / readyGroupSizes.length;

            console.log(`Ready matches - Largest group: ${readyMaxSize} products`);
            console.log(`Ready matches - Average group size: ${readyAvgSize.toFixed(2)} products`);
        }

        console.log(`\nFINAL VERIFICATION:`);
        console.log(`Ready matches unique products: ${readyUniqueProducts}`);
        console.log(`Orphan unique products: ${orphanUniqueProducts}`);
        console.log(`Total final unique products: ${totalFinalProducts}`);

        if (totalFinalProducts === originalUniqueProducts) {
            console.log('SUCCESS: All products preserved throughout the entire process!');
        } else {
            console.log(`ERROR: Lost ${originalUniqueProducts - totalFinalProducts} products in phase 2!`);
        }

    } catch (error) {
        console.error('Error processing matches:', error);
    }
}

processMatches();