const fs = require('fs');

const finalMatchesPath = 'final_matches.json';
const groupedMatchesPath = 'grouped_matches.json'; // This will now contain truly merged original objects
const readyMatchesPath = 'ready_matches.json';
const orphanMatchesPath = 'orphan_matches.json';

// Function to get the seller from a product key
function getSeller(productKey) {
    return productKey.split('_')[0];
}

async function processMatches() {
    try {
        const data = await fs.promises.readFile(finalMatchesPath, 'utf8');
        const finalMatches = JSON.parse(data);

        console.log(`Objects before processing (final_matches.json): ${finalMatches.length}`);

        const consolidatedGroups = []; // This will hold the final, fully merged objects
        const processedOriginalIndices = new Set(); // To track original objects that have already been merged

        // Step 1: Consolidate all objects that are interconnected by common product keys
        for (let i = 0; i < finalMatches.length; i++) {
            // If this original object has already been processed (merged into another group), skip it
            if (processedOriginalIndices.has(i)) {
                continue;
            }

            // Start a new consolidated group with the current object
            let currentConsolidatedObject = { ...finalMatches[i] };
            processedOriginalIndices.add(i);

            // Keep track of all keys present in the current consolidated group
            // This set will expand as more objects are merged
            const keysInCurrentConsolidatedGroup = new Set(Object.keys(currentConsolidatedObject));

            let changedInThisIteration = true; // Flag to continue merging as long as new merges occur
            while (changedInThisIteration) {
                changedInThisIteration = false; // Reset for the current pass

                // Iterate through all original objects again to find any new objects to merge
                // This inner loop is crucial for transitive merging
                for (let j = 0; j < finalMatches.length; j++) {
                    // If this object is already processed or is the same as the starting object, skip
                    if (processedOriginalIndices.has(j)) {
                        continue;
                    }

                    const otherObject = finalMatches[j];
                    const otherObjectKeys = Object.keys(otherObject);

                    let hasCommonKey = false;
                    // Check if 'otherObject' shares any key with the 'currentConsolidatedObject'
                    for (const key of otherObjectKeys) {
                        if (keysInCurrentConsolidatedGroup.has(key)) {
                            hasCommonKey = true;
                            break;
                        }
                    }

                    if (hasCommonKey) {
                        // Merge the 'otherObject' into 'currentConsolidatedObject'
                        // Note: Object.assign handles duplicate keys by overwriting,
                        // ensuring only unique keys remain in the merged object.
                        Object.assign(currentConsolidatedObject, otherObject);
                        processedOriginalIndices.add(j); // Mark 'otherObject' as processed

                        // Add new keys from the merged object to the set for future comparisons
                        Object.keys(otherObject).forEach(key => keysInCurrentConsolidatedGroup.add(key));
                        changedInThisIteration = true; // A merge occurred, so we need another pass
                    }
                }
            }
            // After the while loop, 'currentConsolidatedObject' contains all transitively connected original objects
            consolidatedGroups.push(currentConsolidatedObject);
        }

        // Save the consolidated match groups to grouped_matches.json
        await fs.promises.writeFile(groupedMatchesPath, JSON.stringify(consolidatedGroups, null, 2));
        console.log(`Objects in grouped_matches.json (after full consolidation): ${consolidatedGroups.length}`);


        const readyMatches = [];
        const orphanMatches = [];
        let objectsWithSingleProduct = 0; // Renamed for clarity
        let objectsWithSameSellerProducts = 0; // Renamed for clarity

        // Step 2 & 3: Categorize the consolidated groups into ready_matches and orphan_matches
        consolidatedGroups.forEach(matchObject => {
            const productKeys = Object.keys(matchObject);
            const sellers = new Set();

            productKeys.forEach(key => {
                sellers.add(getSeller(key));
            });

            // Log how many objects in grouped_matches.json have only one product record
            if (productKeys.length === 1) {
                objectsWithSingleProduct++;
            }

            // Log how many objects in grouped_matches.json have all products from the same seller
            if (sellers.size === 1) {
                objectsWithSameSellerProducts++;
            }

            // An object qualifies for ready_matches if it contains:
            // 1. More than one product (key-value pair) AND
            // 2. Products from more than one distinct seller
            if (productKeys.length > 1 && sellers.size > 1) {
                readyMatches.push(matchObject);
            } else {
                orphanMatches.push(matchObject);
            }
        });

        await fs.promises.writeFile(readyMatchesPath, JSON.stringify(readyMatches, null, 2));
        await fs.promises.writeFile(orphanMatchesPath, JSON.stringify(orphanMatches, null, 2));

        console.log(`Objects after splitting (ready_matches.json + orphan_matches.json): ${readyMatches.length + orphanMatches.length}`);
        console.log(`Objects in grouped_matches.json with only one product record: ${objectsWithSingleProduct}`);
        console.log(`Objects in grouped_matches.json with all products from the same seller: ${objectsWithSameSellerProducts}`);

    } catch (error) {
        console.error('Error processing matches:', error);
    }
}

processMatches();