const fs = require('fs');

// Function to get the seller from a product key
function getSeller(productKey) {
    return productKey.split('_')[0];
}

// Function to count unique products in an array of groups
function countUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts.size;
}

// Function to get all unique products from groups
function getAllUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts;
}

async function properMergePreservingAll() {
    try {
        console.log('=== PROPER MERGE PRESERVING ALL PRODUCTS ===\n');
        
        // Step 1: Read the original final_matches.json
        const data = await fs.promises.readFile('final_matches.json', 'utf8');
        const finalMatches = JSON.parse(data);
        
        console.log(`📊 Input: final_matches.json contains ${finalMatches.length} groups`);
        
        // Count unique products in original data
        const originalUniqueProducts = countUniqueProducts(finalMatches);
        console.log(`📊 Original unique products: ${originalUniqueProducts}`);
        
        // Step 2: Create a graph-based approach to merge groups
        // Build a map of product -> group indices that contain it
        const productToGroupIndices = {};
        finalMatches.forEach((group, groupIndex) => {
            Object.keys(group).forEach(productKey => {
                if (!productToGroupIndices[productKey]) {
                    productToGroupIndices[productKey] = [];
                }
                productToGroupIndices[productKey].push(groupIndex);
            });
        });
        
        // Step 3: Build connected components using Union-Find algorithm
        // This will merge all groups that share any common products
        const parent = Array.from({length: finalMatches.length}, (_, i) => i);
        
        function find(x) {
            if (parent[x] !== x) {
                parent[x] = find(parent[x]);
            }
            return parent[x];
        }
        
        function union(x, y) {
            const rootX = find(x);
            const rootY = find(y);
            if (rootX !== rootY) {
                parent[rootX] = rootY;
            }
        }
        
        // Union groups that share common products
        Object.values(productToGroupIndices).forEach(groupIndices => {
            if (groupIndices.length > 1) {
                // Union all groups that contain this product
                for (let i = 1; i < groupIndices.length; i++) {
                    union(groupIndices[0], groupIndices[i]);
                }
            }
        });
        
        // Step 4: Group the original groups by their connected components
        const componentGroups = {};
        finalMatches.forEach((group, groupIndex) => {
            const root = find(groupIndex);
            if (!componentGroups[root]) {
                componentGroups[root] = [];
            }
            componentGroups[root].push(group);
        });
        
        // Step 5: Merge groups within each connected component
        const mergedGroups = [];
        Object.values(componentGroups).forEach(groupsToMerge => {
            const mergedGroup = {};
            groupsToMerge.forEach(group => {
                Object.assign(mergedGroup, group);
            });
            mergedGroups.push(mergedGroup);
        });
        
        console.log(`📊 After merging: ${mergedGroups.length} groups`);
        
        // Step 6: Verify we preserved all products
        const mergedUniqueProducts = countUniqueProducts(mergedGroups);
        console.log(`📊 Merged unique products: ${mergedUniqueProducts}`);
        
        if (mergedUniqueProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved during merge!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - mergedUniqueProducts} products during merge!`);
            
            // Find missing products
            const originalProducts = getAllUniqueProducts(finalMatches);
            const mergedProducts = getAllUniqueProducts(mergedGroups);
            const missingProducts = [...originalProducts].filter(p => !mergedProducts.has(p));
            
            console.log(`Missing products: ${missingProducts.slice(0, 10).join(', ')}${missingProducts.length > 10 ? '...' : ''}`);
            return; // Don't proceed if we lost products
        }
        
        // Step 7: Save merged groups
        await fs.promises.writeFile('merged_groups.json', JSON.stringify(mergedGroups, null, 2));
        console.log(`📁 Saved: merged_groups.json with ${mergedGroups.length} groups`);
        
        // Step 8: Calculate group statistics
        const groupSizes = mergedGroups.map(group => Object.keys(group).length);
        const maxGroupSize = Math.max(...groupSizes);
        const avgGroupSize = groupSizes.reduce((a, b) => a + b, 0) / groupSizes.length;
        
        console.log(`📊 Largest merged group: ${maxGroupSize} products`);
        console.log(`📊 Average merged group size: ${avgGroupSize.toFixed(2)} products`);
        
        // Step 9: Phase 2 - Separate ready matches from orphans
        console.log('\n=== PHASE 2: SEPARATING READY MATCHES FROM ORPHANS ===');
        
        const readyMatches = [];
        const orphanMatches = [];
        
        mergedGroups.forEach(group => {
            const productKeys = Object.keys(group);
            const sellers = new Set();
            
            productKeys.forEach(key => {
                sellers.add(getSeller(key));
            });
            
            // Ready matches: more than 1 product AND more than 1 seller
            if (productKeys.length > 1 && sellers.size > 1) {
                readyMatches.push(group);
            } else {
                orphanMatches.push(group);
            }
        });
        
        // Step 10: Save phase 2 results
        await fs.promises.writeFile('ready_matches.json', JSON.stringify(readyMatches, null, 2));
        await fs.promises.writeFile('orphan.json', JSON.stringify(orphanMatches, null, 2));
        
        console.log(`📁 Saved: ready_matches.json with ${readyMatches.length} groups`);
        console.log(`📁 Saved: orphan.json with ${orphanMatches.length} groups`);
        
        // Step 11: Final verification
        const readyUniqueProducts = countUniqueProducts(readyMatches);
        const orphanUniqueProducts = countUniqueProducts(orphanMatches);
        const totalFinalProducts = readyUniqueProducts + orphanUniqueProducts;
        
        console.log(`\n=== FINAL VERIFICATION ===`);
        console.log(`📊 Ready matches unique products: ${readyUniqueProducts}`);
        console.log(`📊 Orphan unique products: ${orphanUniqueProducts}`);
        console.log(`📊 Total final unique products: ${totalFinalProducts}`);
        
        if (totalFinalProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved throughout the entire process!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - totalFinalProducts} products in phase 2!`);
        }
        
        // Step 12: Show statistics for ready matches
        const readyGroupSizes = readyMatches.map(group => Object.keys(group).length);
        if (readyGroupSizes.length > 0) {
            const readyMaxSize = Math.max(...readyGroupSizes);
            const readyAvgSize = readyGroupSizes.reduce((a, b) => a + b, 0) / readyGroupSizes.length;
            
            console.log(`\n📊 Ready matches - Largest group: ${readyMaxSize} products`);
            console.log(`📊 Ready matches - Average group size: ${readyAvgSize.toFixed(2)} products`);
            
            // Check for sham_2063721
            let shamGroup = null;
            for (let i = 0; i < readyMatches.length; i++) {
                if (readyMatches[i]['sham_2063721']) {
                    shamGroup = readyMatches[i];
                    console.log(`\n🔍 sham_2063721 found in ready matches group with ${Object.keys(shamGroup).length} products`);
                    break;
                }
            }
            
            if (!shamGroup) {
                // Check in orphans
                for (let i = 0; i < orphanMatches.length; i++) {
                    if (orphanMatches[i]['sham_2063721']) {
                        shamGroup = orphanMatches[i];
                        console.log(`\n🔍 sham_2063721 found in orphan group with ${Object.keys(shamGroup).length} products`);
                        break;
                    }
                }
            }
        }
        
        console.log('\n✅ PROCESS COMPLETED SUCCESSFULLY!');
        
    } catch (error) {
        console.error('❌ Error in proper merge:', error);
    }
}

properMergePreservingAll();
