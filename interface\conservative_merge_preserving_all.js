const fs = require('fs');

// Function to get the seller from a product key
function getSeller(productKey) {
    return productKey.split('_')[0];
}

// Function to count unique products in an array of groups
function countUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts.size;
}

// Function to get all unique products from groups
function getAllUniqueProducts(groups) {
    const uniqueProducts = new Set();
    groups.forEach(group => {
        Object.keys(group).forEach(productKey => {
            uniqueProducts.add(productKey);
        });
    });
    return uniqueProducts;
}

// Function to check if merging two groups would create an oversized group
function wouldCreateOversizedGroup(group1, group2, maxSize = 500) {
    const keys1 = new Set(Object.keys(group1));
    const keys2 = new Set(Object.keys(group2));
    const commonKeys = [...keys1].filter(key => keys2.has(key));
    const combinedSize = keys1.size + keys2.size - commonKeys.length;
    return combinedSize > maxSize;
}

// Function to calculate overlap ratio between two groups
function calculateOverlapRatio(group1, group2) {
    const keys1 = new Set(Object.keys(group1));
    const keys2 = new Set(Object.keys(group2));
    const commonKeys = [...keys1].filter(key => keys2.has(key));
    const minSize = Math.min(keys1.size, keys2.size);
    return minSize > 0 ? commonKeys.length / minSize : 0;
}

async function conservativeMergePreservingAll() {
    try {
        console.log('=== CONSERVATIVE MERGE PRESERVING ALL PRODUCTS ===\n');
        
        // Step 1: Read the original final_matches.json
        const data = await fs.promises.readFile('final_matches.json', 'utf8');
        const finalMatches = JSON.parse(data);
        
        console.log(`📊 Input: final_matches.json contains ${finalMatches.length} groups`);
        
        // Count unique products in original data
        const originalUniqueProducts = countUniqueProducts(finalMatches);
        console.log(`📊 Original unique products: ${originalUniqueProducts}`);
        
        // Step 2: Create a more conservative merging approach
        // Start with all original groups
        let currentGroups = finalMatches.map(group => ({ ...group }));
        
        console.log('🔄 Starting conservative merging process...');
        
        let mergeRound = 1;
        let totalMerges = 0;
        
        while (true) {
            console.log(`\n--- Merge Round ${mergeRound} ---`);
            let mergesInThisRound = 0;
            const newGroups = [];
            const processedIndices = new Set();
            
            for (let i = 0; i < currentGroups.length; i++) {
                if (processedIndices.has(i)) continue;
                
                let currentGroup = { ...currentGroups[i] };
                processedIndices.add(i);
                
                // Look for the best group to merge with this one
                let bestMergeIndex = -1;
                let bestOverlapRatio = 0;
                
                for (let j = i + 1; j < currentGroups.length; j++) {
                    if (processedIndices.has(j)) continue;
                    
                    const otherGroup = currentGroups[j];
                    
                    // Check if they have common products
                    const keys1 = new Set(Object.keys(currentGroup));
                    const keys2 = new Set(Object.keys(otherGroup));
                    const commonKeys = [...keys1].filter(key => keys2.has(key));
                    
                    if (commonKeys.length > 0) {
                        // Check if merging would create an oversized group
                        if (!wouldCreateOversizedGroup(currentGroup, otherGroup)) {
                            const overlapRatio = calculateOverlapRatio(currentGroup, otherGroup);
                            
                            // Prefer merges with higher overlap ratios
                            if (overlapRatio > bestOverlapRatio) {
                                bestOverlapRatio = overlapRatio;
                                bestMergeIndex = j;
                            }
                        }
                    }
                }
                
                // If we found a good merge candidate, merge it
                if (bestMergeIndex !== -1) {
                    Object.assign(currentGroup, currentGroups[bestMergeIndex]);
                    processedIndices.add(bestMergeIndex);
                    mergesInThisRound++;
                    totalMerges++;
                }
                
                newGroups.push(currentGroup);
            }
            
            console.log(`   Merges in this round: ${mergesInThisRound}`);
            console.log(`   Groups after round: ${newGroups.length}`);
            
            // If no merges happened, we're done
            if (mergesInThisRound === 0) {
                console.log('🏁 No more merges possible, stopping.');
                break;
            }
            
            currentGroups = newGroups;
            mergeRound++;
            
            // Safety check to prevent infinite loops
            if (mergeRound > 50) {
                console.log('⚠️  Reached maximum merge rounds, stopping.');
                break;
            }
        }
        
        const mergedGroups = currentGroups;
        
        console.log(`\n📊 After conservative merging: ${mergedGroups.length} groups`);
        console.log(`📊 Total merges performed: ${totalMerges}`);
        
        // Step 3: Verify we preserved all products
        const mergedUniqueProducts = countUniqueProducts(mergedGroups);
        console.log(`📊 Merged unique products: ${mergedUniqueProducts}`);
        
        if (mergedUniqueProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved during merge!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - mergedUniqueProducts} products during merge!`);
            return; // Don't proceed if we lost products
        }
        
        // Step 4: Save merged groups
        await fs.promises.writeFile('merged_groups.json', JSON.stringify(mergedGroups, null, 2));
        console.log(`📁 Saved: merged_groups.json with ${mergedGroups.length} groups`);
        
        // Step 5: Calculate group statistics
        const groupSizes = mergedGroups.map(group => Object.keys(group).length);
        const maxGroupSize = Math.max(...groupSizes);
        const avgGroupSize = groupSizes.reduce((a, b) => a + b, 0) / groupSizes.length;
        
        console.log(`📊 Largest merged group: ${maxGroupSize} products`);
        console.log(`📊 Average merged group size: ${avgGroupSize.toFixed(2)} products`);
        
        // Show size distribution
        const sizeDistribution = {};
        groupSizes.forEach(size => {
            if (size <= 10) {
                sizeDistribution[`1-10`] = (sizeDistribution[`1-10`] || 0) + 1;
            } else if (size <= 50) {
                sizeDistribution['11-50'] = (sizeDistribution['11-50'] || 0) + 1;
            } else if (size <= 100) {
                sizeDistribution['51-100'] = (sizeDistribution['51-100'] || 0) + 1;
            } else if (size <= 500) {
                sizeDistribution['101-500'] = (sizeDistribution['101-500'] || 0) + 1;
            } else {
                sizeDistribution['500+'] = (sizeDistribution['500+'] || 0) + 1;
            }
        });
        
        console.log('\n📊 Group size distribution:');
        Object.entries(sizeDistribution).forEach(([range, count]) => {
            console.log(`   ${range} products: ${count} groups`);
        });
        
        // Step 6: Phase 2 - Separate ready matches from orphans
        console.log('\n=== PHASE 2: SEPARATING READY MATCHES FROM ORPHANS ===');
        
        const readyMatches = [];
        const orphanMatches = [];
        
        mergedGroups.forEach(group => {
            const productKeys = Object.keys(group);
            const sellers = new Set();
            
            productKeys.forEach(key => {
                sellers.add(getSeller(key));
            });
            
            // Ready matches: more than 1 product AND more than 1 seller
            if (productKeys.length > 1 && sellers.size > 1) {
                readyMatches.push(group);
            } else {
                orphanMatches.push(group);
            }
        });
        
        // Step 7: Save phase 2 results
        await fs.promises.writeFile('ready_matches.json', JSON.stringify(readyMatches, null, 2));
        await fs.promises.writeFile('orphan.json', JSON.stringify(orphanMatches, null, 2));
        
        console.log(`📁 Saved: ready_matches.json with ${readyMatches.length} groups`);
        console.log(`📁 Saved: orphan.json with ${orphanMatches.length} groups`);
        
        // Step 8: Final verification
        const readyUniqueProducts = countUniqueProducts(readyMatches);
        const orphanUniqueProducts = countUniqueProducts(orphanMatches);
        const totalFinalProducts = readyUniqueProducts + orphanUniqueProducts;
        
        console.log(`\n=== FINAL VERIFICATION ===`);
        console.log(`📊 Ready matches unique products: ${readyUniqueProducts}`);
        console.log(`📊 Orphan unique products: ${orphanUniqueProducts}`);
        console.log(`📊 Total final unique products: ${totalFinalProducts}`);
        
        if (totalFinalProducts === originalUniqueProducts) {
            console.log('✅ SUCCESS: All products preserved throughout the entire process!');
        } else {
            console.log(`❌ ERROR: Lost ${originalUniqueProducts - totalFinalProducts} products in phase 2!`);
        }
        
        // Step 9: Show statistics for ready matches
        const readyGroupSizes = readyMatches.map(group => Object.keys(group).length);
        if (readyGroupSizes.length > 0) {
            const readyMaxSize = Math.max(...readyGroupSizes);
            const readyAvgSize = readyGroupSizes.reduce((a, b) => a + b, 0) / readyGroupSizes.length;
            
            console.log(`\n📊 Ready matches - Largest group: ${readyMaxSize} products`);
            console.log(`📊 Ready matches - Average group size: ${readyAvgSize.toFixed(2)} products`);
            
            // Check for sham_2063721
            let shamGroup = null;
            for (let i = 0; i < readyMatches.length; i++) {
                if (readyMatches[i]['sham_2063721']) {
                    shamGroup = readyMatches[i];
                    console.log(`\n🔍 sham_2063721 found in ready matches group with ${Object.keys(shamGroup).length} products`);
                    break;
                }
            }
            
            if (!shamGroup) {
                // Check in orphans
                for (let i = 0; i < orphanMatches.length; i++) {
                    if (orphanMatches[i]['sham_2063721']) {
                        shamGroup = orphanMatches[i];
                        console.log(`\n🔍 sham_2063721 found in orphan group with ${Object.keys(shamGroup).length} products`);
                        break;
                    }
                }
            }
        }
        
        console.log('\n✅ PROCESS COMPLETED SUCCESSFULLY!');
        
    } catch (error) {
        console.error('❌ Error in conservative merge:', error);
    }
}

conservativeMergePreservingAll();
